# Luminara 错误处理功能

## 概述

Luminara 现在支持在发生崩溃后继续运行服务器，并允许自定义崩溃报告保存路径。

## 配置选项

在 `config/arclight.conf` 文件中添加以下配置：

```hocon
error-handling {
  # 是否在崩溃后继续运行服务器
  # 默认: false (推荐保持关闭)
  # 警告: 启用此选项可能导致数据损坏或不稳定状态
  continue-on-crash = false
  
  # 崩溃报告文件存储目录
  # 默认: "crash-reports"
  crash-report-directory = "crash-reports"
}
```

## 功能说明

### 1. 崩溃后继续运行 (continue-on-crash)

- **默认值**: `false`
- **推荐设置**: 保持 `false`
- **作用**: 当设置为 `true` 时，服务器在遇到崩溃异常后不会停止，而是继续运行
- **风险**: 可能导致数据损坏、内存泄漏或其他不稳定状态

### 2. 崩溃报告目录 (crash-report-directory)

- **默认值**: `"crash-reports"`
- **作用**: 指定崩溃报告文件的保存目录
- **支持**: 相对路径和绝对路径

## 使用场景

### 适合启用 continue-on-crash 的情况：
- 开发和测试环境
- 需要高可用性且有完善监控的环境
- 临时调试特定问题

### 不适合启用的情况：
- 生产环境（除非有特殊需求）
- 没有完善备份策略的服务器
- 处理重要数据的服务器

## 日志信息

当启用 `continue-on-crash` 时，你会在日志中看到：

```
[WARN] 警告: 崩溃后继续运行已启用，这可能导致数据损坏
[INFO] 根据配置，服务器将在崩溃后继续运行
```

当禁用时：
```
[INFO] 崩溃后继续运行已禁用，服务器将停止
```

## 注意事项

1. **数据安全**: 启用此功能前请确保有完整的备份策略
2. **监控**: 建议配合完善的监控系统使用
3. **日志检查**: 定期检查崩溃报告以解决根本问题
4. **测试**: 在生产环境使用前请在测试环境充分验证

## 技术实现

- 修改了 `MinecraftServerMixin` 中的异常处理逻辑
- 添加了 `ArclightCrashHandler` 来处理崩溃决策
- 支持自定义崩溃报告保存路径
- 保持了原有的崩溃报告生成功能

## 配置示例

```hocon
# 开发环境配置
error-handling {
  continue-on-crash = true
  crash-report-directory = "debug-crashes"
}

# 生产环境配置（推荐）
error-handling {
  continue-on-crash = false
  crash-report-directory = "crash-reports"
}
```
