logo = [
  ""
  ""
  "    §1    __                    _                       "
  "    §9   / /   __  ______ ___  (_)___  ____ __________ _"
  "    §3  / /   / / / / __ `__ \\/ / __ \\/ __ `/ ___/ __ `/"
  "    §6 / /___/ /_/ / / / / / / / / / / /_/ / /  / /_/ / "
  "    §e/_____/\\__,_/_/ /_/ /_/_/_/ /_/\\__,_/_/   \\__,_/  "
  ""
  "    §eLuminara·流明纳拉 服务端 By§b QianMoo0121(QianMo_ProMax)"
  "    §a运行版本 {} / {}"
  "    §a构建日期 {}"
  ""
]
release-name {
  Horn = "角"
  GreatHorn = "大角"
  Executions = "折威"
  Trials = "顿顽 1.20.1"
}
java {
  deprecated = [
    "您正在使用过时的 Java 版本"
    "当前版本 {0} 推荐使用 {1}"
    "该版本的 Java 未来将不受支持"
  ]
}

i18n {
  using-language = "正在使用 {0} 语言，{1} 作为备选语言"
}
loading-mapping = "正在加载混淆数据 ..."
mixin-load {
  core = "核心 Mixin 配置已加载"
  optimization = "优化 Mixin 配置已加载"
}
mod-load = "Luminara Mod 已加载"
server-initialization-completed = "Luminara 服务器初始化完成"
patcher {
  loading = "正在加载 Plugin Patcher ..."
  loaded = "加载了 {} 个 Patcher"
  load-error = "加载 Patcher 时发生错误"
}
registry {
  forge-event = "Luminara 事件系统已注册"
  begin = "正在向 Bukkit 注册 ..."
  error = "处理 Forge 注册时出错 "

  material = "注册了 {} 个材料，其中 {} 个方块 {} 个物品"
  entity-type = "注册了 {} 个新的生物类型"
  environment = "注册了 {} 个新的世界类型"
  villager-profession = "注册了 {} 个新的村民职业"
  biome = "注册了 {} 个新的生物群系"
  meta-type {
    not-subclass = "{} 不是 {} 的子类"
    error = "{} 提供的 itemMetaType {} 无效: {}"
    no-candidate = "{} 未在提供的 itemMetaType {} 找到合适的构造方法"
  }
  block-state {
    not-subclass = "{} 不是 {} 的子类"
    error = "{} 提供的 itemMetaType {} 无效: {}"
    no-candidate = "{} 未在提供的 blockStateClass {} 找到合适的构造方法"
  }
  entity {
    not-subclass = "{} 不是 {} 的子类"
    error = "{} 提供的 entityClass {} 无效: {}"
  }

}
error-symlink = "文件系统不支持符号链接"
symlink-file-exist = "创建符号链接 {} 时文件已存在"

# Bootstrap messages
bootstrap {
  error = "Luminara 引导程序错误"
}

# Component bridge messages
component {
  bridge {
    method-not-found = "在 Component 类中找不到 getSiblings 方法"
    init-failed = "初始化 ComponentBridgeHandler 失败: {}"
    get-siblings-failed = "从 Component 获取 siblings 失败: {}"
    create-stream-failed = "创建 Component stream 失败: {}"
    create-iterator-failed = "创建 Component iterator 失败: {}"
  }
}

# Material messages
material {
  bad-data-class = "材料 {} 的数据类 {} 无效"
}

# Player messages
player {
  place-in-world-failed = "无法将玩家放置到世界中"
  moved-wrongly = "{} 移动异常！"
  moved-too-quickly = "{} 移动过快！{},{},{}"
  vehicle-moved-too-quickly = "{} ({} 的载具) 移动过快！{},{},{}"
  vehicle-moved-wrongly = "{} ({} 的载具) 移动异常！{}"
  attack-invalid-entity = "玩家 {} 尝试攻击无效实体"
}

# Server lifecycle messages
server {
  starting = "正在启动 Luminara..."
  stopping = "正在关闭 Luminara..."
  stopped = "Luminara 服务器已关闭"
  crash-report-saved = "崩溃报告已保存到: {}"
  crash-report-failed = "无法将崩溃报告保存到磁盘"
  unexpected-exception = "遇到意外异常"
  continuing-after-crash = "根据配置，服务器将在崩溃后继续运行"
  overload-warning = "服务器无法跟上！服务器过载了吗？运行 {} 毫秒或 {} 个tick落后"
  stop-exception = "关闭服务器时发生异常"
  async-world-save {
    starting-shutdown = "服务器关闭期间开始异步世界保存..."
    starting = "开始异步世界保存..."
    timeout-or-failed = "异步世界保存未在超时时间内完成或失败"
  }
  preparing-start-region = "为维度 {} 准备起始区域"
  world-save {
    failed = "保存世界 {} 失败"
    all-successful = "所有世界保存成功"
  }
  threads {
    force-exit = "{} 个线程未正确关闭，强制退出"
  }
}

# Optimization system messages
optimization {
  entity-cleanup {
    starting = "实体清理开始，预计清理 {} 个实体"
    mpem-completed = "Luminara-MPEM 实体清理完成。清理了 {} 个实体 (死亡: {}, 物品: {}, 密集: {}, 过量: {})"
    cancelled = "实体清理已取消"
    level-error = "在维度 {} 中进行实体清理时出错: {}"
    dead-entities-error = "清理死亡实体时出错: {}"
    old-items-error = "清理旧物品时出错: {}"
  }
  chunk {
    unloading = "正在卸载区块 [{}, {}] 在世界 {}"
    unloaded = "已卸载 {} 个区块"
    rate-limit = "区块加载速率限制: 原始 {} -> 限制 {}"
  }
  memory {
    cleanup-start = "开始内存清理..."
    cleanup-complete = "内存清理完成，释放了 {} MB"
    high-usage = "内存使用率过高: {}%"
    gc-triggered = "触发垃圾回收"
    cache-cleanup-completed = "Luminara 缓存清理完成"
    cache-cleanup-error = "缓存清理时发生错误"
    cache-cleanup-failed = "缓存清理失败"
  }
  manager {
    shutdown-error = "优化系统关闭时发生错误"
  }
  thread-pool {
    graceful-shutdown-failed = "{} 线程池未能正常关闭，强制关闭"
    forced-shutdown-failed = "{} 线程池强制关闭后仍未终止"
    shutdown-interrupted = "关闭 {} 线程池时被中断"
  }
  thread {
    uncaught-exception = "线程 {} 中发生未捕获异常"
  }
  async-event {
    shutdown-interrupted = "关闭期间被中断"
  }
  entity-cleaner {
    forcing-cleanup = "强制实体清理..."
    scheduled-cancelled = "计划的实体清理已取消"
  }
  async-ai {
    calculation-error = "AI 计算中发生错误"
  }
  async-collision {
    handling-error = "处理 {} 和 {} 之间的异步碰撞时发生错误"
    calculation-error = "碰撞计算中发生错误"
  }
  async-redstone {
    update-error = "在 {} 处理异步红石更新时发生错误"
    calculation-error = "计算红石信号时发生错误"
  }
  async-ai {
    calculation-error = "异步AI计算时发生错误"
    processing-error = "异步AI处理实体 {} 时发生错误"
  }
  async-collision {
    calculation-error = "异步碰撞计算时发生错误"
    processing-error = "异步碰撞处理实体 {} 时发生错误"
    check-error = "碰撞检查时发生错误"
  }
  async-event {
    disabled-due-to-errors = "由于错误禁用事件类型的异步处理: {}"
    handler-error = "事件 {} 的异步事件处理器发生错误"
    registered = "已注册异步事件: {}"
  }
  error-handling {
    continue-on-crash-warning = "警告: 崩溃后继续运行已启用，这可能导致数据损坏"
    continue-on-crash-disabled = "崩溃后继续运行已禁用，服务器将停止"
  }
}

# Velocity forwarding messages
velocity {
  enabled = "Velocity Modern 转发已启用"
  disabled = "Velocity Modern 转发已禁用"
  loaded-argument-types = "已加载 {} 个集成参数类型"
  failed-load-argument-types = "加载集成参数类型失败，使用默认值"
  forwarding {
    enabled-for-player = "已为玩家启用 Velocity Modern 转发: {} (在线模式: {})"
    processed-successfully = "成功为玩家处理 Velocity 转发: {}"
    packet-exception = "处理来自 {} 的 Velocity 转发数据包时发生异常"
    initialized = "Velocity Modern 转发已初始化"
    player-processed-successfully = "成功为玩家处理 Velocity 转发: {} (在线模式: {})"
  }
  address {
    field-failed-trying-alternatives = "通过 f_129469_ 设置转发地址失败，尝试替代方法"
    reflection-failed-trying-bridge = "通过反射设置地址失败，尝试桥接方法"
    bridge-failed = "通过桥接设置地址失败"
  }
  packet {
    too-small = "Velocity 数据包过小: {} 字节"
  }
  signature {
    mismatch = "Velocity 签名不匹配！"
    verification-error = "验证 Velocity 签名时发生错误"
  }
  login {
    exception-processing = "处理玩家 {} 的 Velocity 登录时发生异常"
  }
  query {
    send-failed = "发送 Velocity 查询数据包失败"
    null-response = "收到空数据的 Velocity 查询响应"
  }
}



# World management messages
world {
  creating = "正在创建世界 {}"
  created = "世界 {} 已创建"
  loading = "正在加载世界 {}"
  loaded = "世界 {} 已加载"
  unloading = "正在卸载世界 {}"
  unloaded = "世界 {} 已卸载"
  saving = "正在保存世界: {}"
  saved-successfully = "世界 {} 保存成功"
  save-error = "保存世界 {} 时发生错误"
}

# Chat system messages
chat {
  empty-message = "{} 试图发送空消息"
  message-too-long = "{} 试图发送过长的消息: {} 个字符"
}

# Player action messages
player {
  dropped-items-quickly = "{} 丢弃物品过快！"
  invalid-hotbar = "{} 试图设置无效的快捷栏选择"
  command-issued = "{} 执行了服务器命令: {}"
}

comments {
  _v.comment = [
    "源代码仓库: https://github.com/QianMoo0121/Luminara"
    "提交反馈/错误报告: https://github.com/QianMoo0121/Luminara/issues"
    ""
    ""
    "配置文件版本号，请勿编辑"
  ]
  locale.comment = "语言/国际化相关设置"
  optimization {
    comment = "服务端优化相关设置"
    cache-plugin-class.comment = "是否缓存插件类以提高性能"
    goal-selector-update-interval.comment = [
      "实体目标选择器的更新间隔"
      "数值越高消耗资源越少"
      "导致实体更改目标速度变慢"
    ]
    entity-optimization {
      comment = "实体优化相关设置"
      disable-entity-collisions.comment = "是否禁用实体碰撞检测"
      entity-cleanup-enabled.comment = "是否启用实体清理功能"
      entity-cleanup-threshold.comment = "触发实体清理的实体数量阈值"
      entity-freeze-timeout.comment = "实体冻结超时时间（毫秒）"
      reduce-entity-updates.comment = "是否减少实体更新频率"
      clean-valuable-items.comment = "是否清理有价值的物品"
      item-max-age.comment = "物品最大存在时间（tick）"
      cleanup-notification-enabled.comment = "是否启用清理通知"
      cleanup-warning-time.comment = "清理警告时间（秒）"
      cleanup-start-message.comment = "清理开始消息模板"
      cleanup-complete-message.comment = "清理完成消息模板"
      cleanup-cancelled-message.comment = "清理取消消息模板"
      entity-check-interval.comment = "实体检查间隔（tick）"
      entity-update-distance.comment = "实体更新距离"
      max-entities-per-chunk.comment = "每个区块最大实体数量"
      max-entities-per-type.comment = "每种类型最大实体数量"
      chunk-entity-limit.comment = "区块实体限制"
    }
    chunk-optimization {
      comment = "区块优化相关设置"
      aggressive-chunk-unloading.comment = "是否启用激进的区块卸载"
      chunk-unload-delay.comment = "区块卸载延迟（tick）"
      optimize-chunk-loading.comment = "是否优化区块加载"
      chunk-load-rate-limit.comment = "区块加载速率限制"
    }
    memory-optimization {
      comment = "内存优化相关设置"
      entity-cleanup-enabled.comment = "是否启用实体清理"
      cache-cleanup-enabled.comment = "是否启用缓存清理"
      cache-cleanup-interval.comment = "缓存清理间隔（秒）"
    }
    async-system {
      comment = "异步系统相关设置"
      enabled.comment = "是否启用异步系统"
      max-threads.comment = "最大线程数"
      async-ai-enabled.comment = "是否启用异步AI"
      async-collision-enabled.comment = "是否启用异步碰撞检测"
      async-redstone-enabled.comment = "是否启用异步红石"
      disable-on-error.comment = "出错时是否禁用异步系统"
      event-class-blacklist.comment = "事件类黑名单"
      mod-blacklist.comment = "模组黑名单"
      strict-class-checking.comment = "是否启用严格类检查"
      timeout-seconds.comment = "超时时间（秒）"
    }
    world-creation {
      comment = "世界创建相关设置"
      fast-world-creation.comment = "是否启用快速世界创建"
      skip-spawn-chunk-loading.comment = "是否跳过出生点区块加载"
      force-close-loading-screen.comment = "是否强制关闭加载屏幕"
      early-world-list-addition.comment = "是否提前添加到世界列表"
      parallel-world-initialization.comment = "是否并行初始化世界"
      world-init-timeout-seconds.comment = "世界初始化超时时间（秒）"
      max-concurrent-world-loads.comment = "最大并发世界加载数"
      optimize-world-border-setup.comment = "是否优化世界边界设置"
      defer-spawn-area-preparation.comment = "是否延迟出生区域准备"
      spawn-area-radius.comment = "出生区域半径"
      async-world-data-loading.comment = "是否异步加载世界数据"
    }
  }
  async-catcher.comment = [
    "异步捕获相关设置"
    "Async Catcher 共有四种模式"
    "NONE - 保持在当前线程执行"
    "DISPATCH - 不阻塞地发布到主线程"
    "BLOCK - 发布到主线程并等待结果"
    "EXCEPTION - 抛出异常"
  ]
  async-catcher.dump.comment = "是否在 debug 日志中打印堆栈信息"
  async-world-save.comment = [
    "异步世界保存相关设置"
    "在服务器关闭时异步保存世界数据，减少关服等待时间"
  ]
  error-handling.comment = [
    "错误处理相关设置"
    "控制服务器在遇到崩溃和错误时的行为"
  ]
  error-handling.continue-on-crash.comment = "是否在崩溃后继续运行服务器（默认关闭，建议保持关闭）"
  error-handling.crash-report-directory.comment = "崩溃报告文件存储目录"
  async-world-save.enabled.comment = "是否启用异步世界保存功能"
  async-world-save.timeout-seconds.comment = [
    "异步保存超时时间（秒）"
    "如果在指定时间内保存未完成，服务器将继续关闭流程"
  ]
  async-world-save.save-world-data.comment = "是否在异步保存中包含世界数据"
  compatibility {
    symlink-world.comment = [
      "为模组的维度创建符号链接"
      "推荐启用以增强插件交互兼容性"
      "变更此设置会导致模组世界名称变化，可能造成依赖世界名称的插件数据丢失"
      "参见 https://github.com/IzzelAliz/Arclight/wiki/World-Symlink"
    ]
    extra-logic-worlds.comment = [
      "额外运行逻辑的维度类名"
      "如果有模组世界/功能运行不正常，尝试在日志中搜索和 [EXT_LOGIC] 有关的对应类名并添加"
    ]
    forward-permission.comment = [
      "true - 将 Forge 权限查询请求转发至 Bukkit"
      "false - 不启用权限转发"
      "reverse - 将 Bukkit 玩家权限查询请求转发至 Forge"
    ]
    valid-username-regex.comment = [
      "用户名合法检查正则表达式，留空为使用原版检查"
      "如果需要允许中文字符可以使用"
      "valid-username-regex = \"^[ -~\\p{sc=Han}]{1,16}$\""
      "如果允许任何用户名可以使用"
      "valid-username-regex = \".+\""
    ]
    lenient-item-tag-match.comment = [
      "允许空 NBT 标签的物品和没有 NBT 标签的物品堆叠"
    ]
  }
  velocity {
    comment = "Velocity Modern 转发相关设置"
    enabled.comment = [
      "是否启用 Velocity Modern 转发支持"
      "启用后可以与 Velocity 代理服务器配合使用"
    ]
    online-mode.comment = [
      "是否启用在线模式验证"
      "通常应与 Velocity 配置中的 online-mode 设置保持一致"
    ]
    forwarding-secret.comment = [
      "Velocity 转发密钥"
      "必须与 Velocity 配置文件中的 forwarding-secret 完全一致"
      "用于验证来自 Velocity 的连接请求"
    ]
    debug-logging.comment = [
      "是否在日志中显示 Velocity 转发相关的调试信息"
      "启用后可以帮助诊断玩家连接问题"
    ]
  }
}

# Authentication messages
auth {
  verification-failed-allow = "用户名验证失败，但仍允许进入！"
  invalid-session = "用户名 '{}' 尝试使用无效会话加入"
  servers-down-allow = "认证服务器宕机，但仍允许进入！"
  servers-unavailable = "无法验证用户名，因为服务器不可用"
  exception-verifying = "验证 {} 时发生异常"
  player-uuid = "玩家 {} 的 UUID 是 {}"
  exception-verifying-player = "验证玩家 {} 时发生异常"
  player-uuid-velocity = "玩家 {} 的 UUID 是 {} (来自 Velocity)"
}

# World messages
world {
  unknown-level-stem = "将 {} 分配给未知的世界类型 {}"
}

# Chunk messages
chunk {
  unload-callback {
    failed = "为区块 {} 安排卸载回调失败"
  }
  load-callback {
    failed = "为区块 {} 安排加载回调失败"
  }
}

# Entity messages
entity {
  target {
    unknown-reason = "未知目标原因，将 {} 的目标设置为 {}"
  }
}

# Item messages
item {
  legacy-itemstack = "正在使用旧版 ItemStack，更新将不会应用: {}"
}

# Recipe messages
recipe {
  loading {
    skip-null-serializer = "跳过加载配方 {}，因为其序列化器返回 null"
    parsing-error = "加载配方 {} 时解析错误"
    completed = "已加载 {} 个配方"
  }
}

# Loot messages
loot {
  container {
    overfill-attempt = "尝试过度填充容器"
  }
}

# Player data messages
player {
  data {
    load-failed = "加载玩家 {} 的数据失败"
  }
}

# Permission messages
permission {
  forge-to-bukkit = "将 forge 权限[{}]转发到 bukkit"
}

# ClassLoader messages
classloader {
  dump-failed = "转储类 {} 失败"
  client-side-class = "加载客户端类: {}"
  no-remap-config = "类 {} 没有提供重映射配置，使用 PLUGIN"
  class-not-found = "找不到类 {}"
}



# Event system messages
event {
  handler {
    registration-failed = "注册事件处理器时发生错误: {} {}"
  }
}

# Enum system messages
enum {
  field-added = "已将 {} 添加到 {}"
}

# Chat system messages
chat {
  processing-exception = "处理聊天事件时发生异常"
}

# Network messages
network {
  custom-payload {
    register-too-large = "自定义载荷 REGISTER 过大: {} 字节"
    register-string-too-long = "自定义载荷 REGISTER 字符串过长: {} 个字符"
    register-error = "无法注册自定义载荷"
    unregister-too-large = "自定义载荷 UNREGISTER 过大: {} 字节"
    unregister-string-too-long = "自定义载荷 UNREGISTER 字符串过长: {} 个字符"
    unregister-error = "无法注销自定义载荷"
    dispatch-failed = "无法分发自定义载荷"
  }
  packet {
    handle-failed = "处理数据包 {} 失败，抑制错误"
  }

}
