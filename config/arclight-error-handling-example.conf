# Luminara 错误处理配置示例
# 这个文件展示了如何配置 arclight.conf 中的 error-handling 部分

error-handling {
  # 是否在崩溃后继续运行服务器
  # 默认: false (推荐保持关闭)
  # 警告: 启用此选项可能导致数据损坏或不稳定状态
  continue-on-crash = false
  
  # 在指定时间窗口内允许的最大崩溃次数
  # 超过此数量将强制停止服务器
  # 默认: 3
  max-crash-tolerance = 3
  
  # 崩溃容忍时间窗口（分钟）
  # 在此时间内的崩溃会被计入容忍度
  # 默认: 10
  crash-tolerance-window-minutes = 10
  
  # 是否在关键错误时自动重启服务器
  # 默认: false
  auto-restart-on-critical-error = false
  
  # 被视为关键错误的异常模式列表
  # 这些错误即使启用 continue-on-crash 也会停止服务器
  critical-error-patterns = [
    "OutOfMemoryError",
    "StackOverflowError", 
    "VirtualMachineError",
    "InternalError"
  ]
  
  # 是否记录详细的崩溃信息
  # 默认: true
  log-crash-details = true
  
  # 是否生成崩溃报告文件
  # 默认: true
  generate-crash-reports = true
  
  # 崩溃报告文件存储目录
  # 默认: "crash-reports"
  crash-report-directory = "crash-reports"
  
  # 是否在崩溃时通知管理员
  # 默认: true
  notify-admins-on-crash = true
  
  # 崩溃恢复延迟时间（秒）
  # 在继续运行前等待的时间
  # 默认: 5
  crash-recovery-delay-seconds = 5
  
  # 是否启用崩溃预防机制
  # 包括垃圾回收和其他清理操作
  # 默认: true
  enable-crash-prevention = true
  
  # 是否启用严格错误处理模式
  # 在严格模式下，更多错误会导致服务器停止
  # 默认: false
  strict-error-handling = false
}

# 使用场景示例:

# 1. 生产环境（推荐）
# continue-on-crash = false
# strict-error-handling = true
# auto-restart-on-critical-error = true

# 2. 开发/测试环境
# continue-on-crash = true
# max-crash-tolerance = 5
# strict-error-handling = false

# 3. 高可用性环境
# continue-on-crash = true
# max-crash-tolerance = 1
# auto-restart-on-critical-error = true
# crash-recovery-delay-seconds = 10

# 管理命令:
# /arclight-error status - 查看错误处理状态
# /arclight-error clear-history - 清除崩溃历史
# /arclight-error toggle-continue - 查看当前设置
# /arclight-error test-crash - 测试崩溃处理（仅用于测试）

# 注意事项:
# 1. continue-on-crash 默认关闭是有原因的 - 崩溃通常表示严重问题
# 2. 启用此功能前请确保有完整的备份策略
# 3. 监控日志以了解崩溃模式和频率
# 4. 在生产环境中谨慎使用此功能
# 5. 定期检查崩溃报告以解决根本问题
