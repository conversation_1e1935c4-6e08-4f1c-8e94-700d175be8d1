package io.izzel.arclight.common.command;

import io.izzel.arclight.common.mod.util.ArclightCrashHandler;
import io.izzel.arclight.i18n.ArclightConfig;
import io.izzel.arclight.i18n.conf.ErrorHandlingSpec;
import net.minecraft.commands.CommandSourceStack;
import net.minecraft.commands.Commands;
import net.minecraft.network.chat.Component;
import com.mojang.brigadier.CommandDispatcher;
import com.mojang.brigadier.context.CommandContext;

/**
 * Command for managing error handling and crash recovery
 */
public class ArclightErrorCommand {
    
    public static void register(CommandDispatcher<CommandSourceStack> dispatcher) {
        dispatcher.register(Commands.literal("arclight-error")
            .requires(source -> source.hasPermission(4)) // Op level 4
            .then(Commands.literal("status")
                .executes(ArclightErrorCommand::showStatus))
            .then(Commands.literal("clear-history")
                .executes(ArclightErrorCommand::clearHistory))
            .then(Commands.literal("toggle-continue")
                .executes(ArclightErrorCommand::toggleContinueOnCrash))
            .then(Commands.literal("test-crash")
                .executes(ArclightErrorCommand::testCrash))
        );
    }
    
    private static int showStatus(CommandContext<CommandSourceStack> context) {
        CommandSourceStack source = context.getSource();
        ErrorHandlingSpec config = ArclightConfig.spec().getErrorHandling();
        
        if (config == null) {
            source.sendSuccess(() -> Component.literal("§cError handling configuration not available"), false);
            return 0;
        }
        
        source.sendSuccess(() -> Component.literal("§6=== Arclight Error Handling Status ==="), false);
        source.sendSuccess(() -> Component.literal("§eContinue on crash: " + (config.isContinueOnCrash() ? "§aEnabled" : "§cDisabled")), false);
        source.sendSuccess(() -> Component.literal("§eMax crash tolerance: §f" + config.getMaxCrashTolerance()), false);
        source.sendSuccess(() -> Component.literal("§eTolerance window: §f" + config.getCrashToleranceWindowMinutes() + " minutes"), false);
        source.sendSuccess(() -> Component.literal("§eTotal crashes: §f" + ArclightCrashHandler.getTotalCrashCount()), false);
        source.sendSuccess(() -> Component.literal("§eRecent crashes: §f" + ArclightCrashHandler.getRecentCrashCount()), false);
        source.sendSuccess(() -> Component.literal("§eStrict error handling: " + (config.isStrictErrorHandling() ? "§aEnabled" : "§cDisabled")), false);
        source.sendSuccess(() -> Component.literal("§eAuto restart on critical: " + (config.isAutoRestartOnCriticalError() ? "§aEnabled" : "§cDisabled")), false);
        
        return 1;
    }
    
    private static int clearHistory(CommandContext<CommandSourceStack> context) {
        CommandSourceStack source = context.getSource();
        
        ArclightCrashHandler.clearCrashHistory();
        source.sendSuccess(() -> Component.literal("§aCrash history cleared"), false);
        
        return 1;
    }
    
    private static int toggleContinueOnCrash(CommandContext<CommandSourceStack> context) {
        CommandSourceStack source = context.getSource();
        
        // Note: This would require runtime configuration modification
        // For now, just show current status and warn about config file changes
        ErrorHandlingSpec config = ArclightConfig.spec().getErrorHandling();
        
        if (config == null) {
            source.sendSuccess(() -> Component.literal("§cError handling configuration not available"), false);
            return 0;
        }
        
        boolean current = config.isContinueOnCrash();
        source.sendSuccess(() -> Component.literal("§eContinue on crash is currently: " + (current ? "§aEnabled" : "§cDisabled")), false);
        source.sendSuccess(() -> Component.literal("§6To change this setting, edit arclight.conf and restart the server"), false);
        source.sendSuccess(() -> Component.literal("§cWarning: Enabling continue-on-crash can lead to data corruption!"), false);
        
        return 1;
    }
    
    private static int testCrash(CommandContext<CommandSourceStack> context) {
        CommandSourceStack source = context.getSource();
        
        source.sendSuccess(() -> Component.literal("§cGenerating test crash in 3 seconds..."), false);
        source.sendSuccess(() -> Component.literal("§eThis will test the crash handling system"), false);
        
        // Schedule a test crash
        new Thread(() -> {
            try {
                Thread.sleep(3000);
                throw new RuntimeException("Test crash generated by arclight-error test-crash command");
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }).start();
        
        return 1;
    }
}
